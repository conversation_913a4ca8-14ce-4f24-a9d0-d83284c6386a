import { useEffect, useState } from 'react';
import { <PERSON>, Text, <PERSON><PERSON>, Spinner, YStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { useSetSendPackage } from './useSendPackageStore';
import { useUpdateRequestPickup } from './useRequestPickupStore';
import { useSetOrderAddress } from '../useLastOrderStore';
import { Dimensions } from 'react-native';

export function SelectLocation() {
  const router = useRouter();
  const { type } = useLocalSearchParams<{ type: 'pickup' | 'dropoff' | 'request' | 'orderaddress' }>();
  const setPickup = useSetSendPackage((s) => s.setPickup);
  const setDropoff = useSetSendPackage((s) => s.setDropoff);
  const updateField = useUpdateRequestPickup((r) => r.updateField);
  const setOrderAddress = useSetOrderAddress((o) => o.setAddress);

  const [marker, setMarker] = useState<[number, number] | null>(null); // [lng, lat]
  const [region, setRegion] = useState<[number, number] | null>(null);
  const [address, setAddress] = useState<string | null>(null);
  const [addressLoading, setAddressLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const { width, height } = Dimensions.get('window');

  useEffect(() => {
    (async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        alert('Location permission denied. Defaulting to Nablus.');
        const lngLat: [number, number] = [35.2544, 32.2211];
        setRegion(lngLat);
        setMarker(lngLat);
        reverseGeocode(lngLat[1], lngLat[0]);
        setLoading(false);
        return;
      }

      const loc = await Location.getCurrentPositionAsync({});
      const lngLat: [number, number] = [loc.coords.longitude, loc.coords.latitude];
      setRegion(lngLat);
      setMarker(lngLat);
      reverseGeocode(loc.coords.latitude, loc.coords.longitude);
      setLoading(false);
    })();
  }, []);

  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      setAddressLoading(true);
      const geo = await Location.reverseGeocodeAsync({ latitude: lat, longitude: lng });
      if (!geo || geo.length === 0) {
        setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
        return;
      }
      const g = geo[0];
      const formatted = `${g.name || ''}, ${g.city || ''}, ${g.region || ''}`.trim();
      setAddress(formatted || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } catch (e) {
      setAddress(`Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`);
    } finally {
      setAddressLoading(false);
    }
  };

  const onMapPress = (e: any) => {
    const coords = e.geometry.coordinates;
    setMarker(coords);
    reverseGeocode(coords[1], coords[0]); // or use debouncedReverseGeocode
  };

  const handleSelect = () => {
    if (!marker) return;
    const [lng, lat] = marker;

    const locationData = {
      lat,
      lng,
      address: address || `Lat: ${lat.toFixed(4)}, Lng: ${lng.toFixed(4)}`,
    };

    if (type === 'pickup') setPickup(locationData);
    else if(type === 'dropoff') setDropoff(locationData);
    else if(type === 'request') updateField('pickup', locationData);
    else setOrderAddress(locationData);

    router.back();
  };

  if (loading || !region) {
    return (
      <View flex={1} ai="center" jc="center">
        <Spinner size="large" color="$primary" />
        <Text mt="$3">Fetching location...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Mock Map for Location Selection */}
      <View
        style={{
          width,
          height,
          backgroundColor: '#e8f4f8',
          position: 'relative',
          justifyContent: 'center',
          alignItems: 'center'
        }}
        onTouchEnd={(e) => {
          const { locationX, locationY } = e.nativeEvent;
          // Convert touch coordinates to lat/lng (mock calculation)
          const lat = region[1] + (height/2 - locationY) * 0.0001;
          const lng = region[0] + (locationX - width/2) * 0.0001;
          onMapPress([lng, lat]);
        }}
      >
        {/* Grid Pattern */}
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1
        }}>
          {Array.from({ length: 20 }).map((_, i) => (
            <View key={`h-${i}`} style={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: i * (height / 20),
              height: 1,
              backgroundColor: '#666'
            }} />
          ))}
          {Array.from({ length: 15 }).map((_, i) => (
            <View key={`v-${i}`} style={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: i * (width / 15),
              width: 1,
              backgroundColor: '#666'
            }} />
          ))}
        </View>

        {/* Center Location */}
        <View style={{
          width: 20,
          height: 20,
          borderRadius: 10,
          backgroundColor: '#007AFF',
          borderWidth: 3,
          borderColor: '#fff',
        }} />

        {/* Selected Marker */}
        {marker && (
          <View
            style={{
              position: 'absolute',
              left: width/2 + (marker[0] - region[0]) * 10000 - 15,
              top: height/2 - (marker[1] - region[1]) * 10000 - 15,
              width: 30,
              height: 30,
              borderRadius: 15,
              backgroundColor: 'purple',
              borderWidth: 2,
              borderColor: 'white',
            }}
          />
        )}
      </View>

      <YStack
        position="absolute"
        bottom={30}
        width="100%"
        px="$4"
        gap="$3"
        zIndex={10}
        pointerEvents="box-none"
      >
        {addressLoading ? (
          <Text fontSize="$5" textAlign="center" color="$gray10">
            Fetching address...
          </Text>
        ) : address ? (
          <Text fontSize="$5" textAlign="center" color="$primary">
            {address}
          </Text>
        ) : null}

        <Button
          size="$5"
          bg="$primary"
          color="white"
          icon={<Ionicons name="checkmark" size={20} color="white" />}
          disabled={!marker}
          onPress={handleSelect}
          hoverStyle={{ bg: '$third' }}
          pressStyle={{ bg: '$third' }}
        >
          Confirm Location
        </Button>
      </YStack>
    </View>
  );
}
