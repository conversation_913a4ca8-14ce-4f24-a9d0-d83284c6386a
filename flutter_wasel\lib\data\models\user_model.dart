// import 'package:json_annotation/json_annotation.dart';

// part 'user_model.g.dart';

class UserModel {
  final String id;
  final String email;
  final String role;
  final String? supplierId;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final AddressModel? address;
  final bool isEmailVerified;
  final DateTime? lastLogin;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.email,
    required this.role,
    this.supplierId,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.address,
    required this.isEmailVerified,
    this.lastLogin,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
    id: json['id'] as String,
    email: json['email'] as String,
    role: json['role'] as String,
    supplierId: json['supplierId'] as String?,
    firstName: json['firstName'] as String?,
    lastName: json['lastName'] as String?,
    phoneNumber: json['phoneNumber'] as String?,
    address: json['address'] != null ? AddressModel.fromJson(json['address'] as Map<String, dynamic>) : null,
    isEmailVerified: json['isEmailVerified'] as bool,
    lastLogin: json['lastLogin'] != null ? DateTime.parse(json['lastLogin'] as String) : null,
    isActive: json['isActive'] as bool,
    createdAt: DateTime.parse(json['createdAt'] as String),
    updatedAt: DateTime.parse(json['updatedAt'] as String),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'email': email,
    'role': role,
    if (supplierId != null) 'supplierId': supplierId,
    if (firstName != null) 'firstName': firstName,
    if (lastName != null) 'lastName': lastName,
    if (phoneNumber != null) 'phoneNumber': phoneNumber,
    if (address != null) 'address': address!.toJson(),
    'isEmailVerified': isEmailVerified,
    if (lastLogin != null) 'lastLogin': lastLogin!.toIso8601String(),
    'isActive': isActive,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  UserModel copyWith({
    String? id,
    String? email,
    String? role,
    String? supplierId,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    AddressModel? address,
    bool? isEmailVerified,
    DateTime? lastLogin,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      role: role ?? this.role,
      supplierId: supplierId ?? this.supplierId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      lastLogin: lastLogin ?? this.lastLogin,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return email.split('@').first;
  }

  bool get isCustomer => role == 'customer';
  bool get isSupplier => role == 'supplier';
  bool get isAdmin => role == 'admin';
}

class AddressModel {
  final String? street;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;

  const AddressModel({
    this.street,
    this.city,
    this.state,
    this.zipCode,
    this.country,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) => AddressModel(
    street: json['street'] as String?,
    city: json['city'] as String?,
    state: json['state'] as String?,
    zipCode: json['zipCode'] as String?,
    country: json['country'] as String?,
  );

  Map<String, dynamic> toJson() => {
    if (street != null) 'street': street,
    if (city != null) 'city': city,
    if (state != null) 'state': state,
    if (zipCode != null) 'zipCode': zipCode,
    if (country != null) 'country': country,
  };

  AddressModel copyWith({
    String? street,
    String? city,
    String? state,
    String? zipCode,
    String? country,
  }) {
    return AddressModel(
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
    );
  }

  String get fullAddress {
    final parts = <String>[];
    if (street != null && street!.isNotEmpty) parts.add(street!);
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (state != null && state!.isNotEmpty) parts.add(state!);
    if (zipCode != null && zipCode!.isNotEmpty) parts.add(zipCode!);
    if (country != null && country!.isNotEmpty) parts.add(country!);
    return parts.join(', ');
  }
}
