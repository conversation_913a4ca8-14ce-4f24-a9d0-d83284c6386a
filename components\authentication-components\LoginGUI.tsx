import { <PERSON><PERSON><PERSON>ck, XS<PERSON>ck, H4, H6, <PERSON>, Theme, Card } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link, useRouter } from 'expo-router';
import Login<PERSON>ogo from '../LoginLogo';
import { Alert, ScrollView, Dimensions } from 'react-native';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/api';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    const router = useRouter();
    const { handleSubmit, getValues } = methods;
    const { setCurrentUser, setLoading, setError } = useCurrentUserData();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = async () => {
      const { ['username or email']: email, password } = getValues();

      if (!email || !password) {
        Alert.alert('Login Failed', 'Please enter both email and password.');
        return;
      }

      setIsSubmitting(true);
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.login({
          email: email.trim().toLowerCase(),
          password,
        });

        if (response.success && response.data) {
          setCurrentUser(response.data.user);

          // Redirect based on role
          if (response.data.user.role === 'customer') {
            router.push('/(customer-pages)/home');
          } else if (response.data.user.role === 'supplier') {
            router.push('/(supplier-pages)/home');
          }
        } else {
          Alert.alert('Login Failed', response.message || 'Invalid email or password.');
          setError(response.message || 'Login failed');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Network error occurred';
        Alert.alert('Login Failed', errorMessage);
        setError(errorMessage);
      } finally {
        setIsSubmitting(false);
        setLoading(false);
      }
    };


    const { width } = Dimensions.get('window');

    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack flex={1} backgroundColor="$background">
                    {/* Header Section */}
                    <YStack
                        padding="$6"
                        paddingTop="$8"
                        backgroundColor="$primary"
                        borderBottomLeftRadius="$6"
                        borderBottomRightRadius="$6"
                        alignItems="center"
                        gap="$3"
                    >
                        <LoginLogo />
                        <H4 color="white" fontWeight="bold" textAlign="center">
                            Welcome Back!
                        </H4>
                        <Text color="white" fontSize="$4" textAlign="center" opacity={0.9}>
                            Sign in to continue your journey
                        </Text>
                    </YStack>

                    {/* Content Section */}
                    <ScrollView
                        contentContainerStyle={{
                            flexGrow: 1,
                            padding: 24,
                            paddingBottom: 120
                        }}
                        showsVerticalScrollIndicator={false}
                    >
                        <MotiView
                            from={{ opacity: 0, translateY: 50 }}
                            animate={{ opacity: 1, translateY: 0 }}
                            transition={{ type: 'timing', duration: 500 }}
                        >
                            <Card
                                padding="$6"
                                borderRadius="$6"
                                backgroundColor="white"
                                shadowColor="$shadowColor"
                                shadowOffset={{ width: 0, height: 4 }}
                                shadowOpacity={0.1}
                                shadowRadius={8}
                                elevation={5}
                                marginTop="$4"
                            >
                                <YStack gap="$5">
                                    <YStack gap="$2" alignItems="center">
                                        <Ionicons name="log-in" size={32} color="#7529B3" />
                                        <Text fontSize="$5" fontWeight="600" color="$gray11" textAlign="center">
                                            Sign In to Your Account
                                        </Text>
                                    </YStack>

                                    <YStack gap="$4">
                                        <CustomTextField
                                            name="username or email"
                                            icon="person"
                                            label="Username or Email"
                                            placeholder="Enter your username or email"
                                            keyboardType="email-address"
                                            autoCapitalize="none"
                                            required
                                        />

                                        <CustomTextField
                                            name="password"
                                            icon="lock-closed"
                                            label="Password"
                                            secureTextEntry
                                            placeholder="Enter your password"
                                            required
                                        />
                                    </YStack>

                                    <Link
                                        href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }}
                                        asChild
                                    >
                                        <Text
                                            color="$primary"
                                            fontSize="$4"
                                            fontWeight="500"
                                            textAlign="center"
                                            textDecorationLine="underline"
                                            padding="$2"
                                        >
                                            Forgot Password? Click Here
                                        </Text>
                                    </Link>
                                </YStack>
                            </Card>
                        </MotiView>
                    </ScrollView>

                    {/* Bottom Buttons */}
                    <YStack
                        position="absolute"
                        bottom={0}
                        left={0}
                        right={0}
                        backgroundColor="white"
                        padding="$4"
                        borderTopWidth={1}
                        borderTopColor="$borderColor"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: -2 }}
                        shadowOpacity={0.1}
                        shadowRadius={4}
                        elevation={5}
                    >
                        <XStack gap="$3" justifyContent="space-between">
                            <Button
                                flex={1}
                                backgroundColor="$primary"
                                color="white"
                                onPress={handleSubmit(onSubmit)}
                                disabled={isSubmitting}
                                icon={
                                    isSubmitting ? (
                                        <Ionicons name="hourglass" size={20} color="white" />
                                    ) : (
                                        <Ionicons name="log-in" size={20} color="white" />
                                    )
                                }
                                fontSize="$5"
                                fontWeight="600"
                                height={56}
                            >
                                {isSubmitting ? "Signing In..." : "Sign In"}
                            </Button>

                            <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                                <Button
                                    flex={1}
                                    backgroundColor="$green8"
                                    color="white"
                                    icon={<Ionicons name="person-add" size={20} color="white" />}
                                    fontSize="$5"
                                    fontWeight="600"
                                    height={56}
                                >
                                    Sign Up
                                </Button>
                            </Link>
                        </XStack>

                        <Text
                            fontSize="$3"
                            color="$gray9"
                            textAlign="center"
                            marginTop="$3"
                        >
                            Don't have an account? Create one now!
                        </Text>
                    </YStack>

                    {children}
                </YStack>
            </FormProvider>
        </Theme>
    );
};