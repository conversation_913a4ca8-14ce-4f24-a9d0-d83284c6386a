import React from 'react';
import { YStack, Text, H5, XStack } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { CustomTextField } from '../../CustomTextField';
import { Ionicons } from '@expo/vector-icons';
import { Pressable } from 'react-native';

export const BusinessInfoStep = () => {
  const { signupData, updateSignupData } = useSignupStore();

  const businessTypes = [
    { value: 'restaurant', label: 'Restaurant', icon: 'restaurant' },
    { value: 'clothing', label: 'Clothing Store', icon: 'shirt' },
    { value: 'grocery', label: 'Grocery Store', icon: 'basket' },
    { value: 'pharmacy', label: 'Pharmacy', icon: 'medical' },
    { value: 'electronics', label: 'Electronics', icon: 'phone-portrait' },
    { value: 'other', label: 'Other', icon: 'business' },
  ];

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="storefront" size={48} color="#7529B3" />
        <H5 textAlign="center" color="$primary">Business Information</H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          Tell us about your business to help customers find you
        </Text>
      </YStack>

      <YStack gap="$4">
        <CustomTextField
          label="Store/Business Name"
          placeholder="Enter your business name"
          value={signupData.storeName || ''}
          onChangeText={(text) => updateSignupData({ storeName: text })}
          icon="storefront"
          required
        />

        <CustomTextField
          label="Operating Hours"
          placeholder="e.g. 9:00 AM - 11:00 PM"
          value={signupData.openHours || ''}
          onChangeText={(text) => updateSignupData({ openHours: text })}
          icon="time"
          required
        />
      </YStack>

      <YStack gap="$3" marginTop="$4">
        <Text fontSize="$4" fontWeight="600" color="$gray11">
          Business Type *
        </Text>
        <YStack gap="$2">
          {businessTypes.map((type, index) => (
            <XStack key={type.value} gap="$2">
              {index % 2 === 0 && businessTypes[index + 1] && (
                <>
                  <Pressable
                    onPress={() => updateSignupData({ businessType: type.value })}
                    style={{
                      flex: 1,
                      padding: 12,
                      borderRadius: 8,
                      borderWidth: 2,
                      borderColor: signupData.businessType === type.value ? '#7529B3' : '#e5e5e5',
                      backgroundColor: signupData.businessType === type.value ? '#f3f0ff' : '#fff',
                      alignItems: 'center',
                      gap: 6,
                    }}
                  >
                    <Ionicons 
                      name={type.icon as any} 
                      size={20} 
                      color={signupData.businessType === type.value ? '#7529B3' : '#666'} 
                    />
                    <Text 
                      fontSize="$2" 
                      fontWeight="500"
                      color={signupData.businessType === type.value ? '$primary' : '$gray10'}
                      textAlign="center"
                    >
                      {type.label}
                    </Text>
                  </Pressable>
                  
                  <Pressable
                    onPress={() => updateSignupData({ businessType: businessTypes[index + 1].value })}
                    style={{
                      flex: 1,
                      padding: 12,
                      borderRadius: 8,
                      borderWidth: 2,
                      borderColor: signupData.businessType === businessTypes[index + 1].value ? '#7529B3' : '#e5e5e5',
                      backgroundColor: signupData.businessType === businessTypes[index + 1].value ? '#f3f0ff' : '#fff',
                      alignItems: 'center',
                      gap: 6,
                    }}
                  >
                    <Ionicons 
                      name={businessTypes[index + 1].icon as any} 
                      size={20} 
                      color={signupData.businessType === businessTypes[index + 1].value ? '#7529B3' : '#666'} 
                    />
                    <Text 
                      fontSize="$2" 
                      fontWeight="500"
                      color={signupData.businessType === businessTypes[index + 1].value ? '$primary' : '$gray10'}
                      textAlign="center"
                    >
                      {businessTypes[index + 1].label}
                    </Text>
                  </Pressable>
                </>
              )}
              {index % 2 === 1 && !businessTypes[index + 1] && (
                <Pressable
                  onPress={() => updateSignupData({ businessType: type.value })}
                  style={{
                    flex: 1,
                    padding: 12,
                    borderRadius: 8,
                    borderWidth: 2,
                    borderColor: signupData.businessType === type.value ? '#7529B3' : '#e5e5e5',
                    backgroundColor: signupData.businessType === type.value ? '#f3f0ff' : '#fff',
                    alignItems: 'center',
                    gap: 6,
                  }}
                >
                  <Ionicons 
                    name={type.icon as any} 
                    size={20} 
                    color={signupData.businessType === type.value ? '#7529B3' : '#666'} 
                  />
                  <Text 
                    fontSize="$2" 
                    fontWeight="500"
                    color={signupData.businessType === type.value ? '$primary' : '$gray10'}
                    textAlign="center"
                  >
                    {type.label}
                  </Text>
                </Pressable>
              )}
            </XStack>
          ))}
        </YStack>
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          This helps customers find the right type of business
        </Text>
      </YStack>
    </YStack>
  );
};
