import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';

class CustomContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? borderRadius;
  final Border? border;
  final List<BoxShadow>? boxShadow;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const CustomContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.boxShadow,
    this.width,
    this.height,
    this.alignment,
  });

  // Factory constructor for card-like container
  factory CustomContainer.card({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? backgroundColor,
    double? borderRadius,
  }) {
    return CustomContainer(
      padding: padding ?? const EdgeInsets.all(AppConstants.spacing16),
      margin: margin,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius ?? AppConstants.borderRadius12,
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: child,
    );
  }

  // Factory constructor for input container
  factory CustomContainer.input({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? backgroundColor,
    Color? borderColor,
    double? borderRadius,
  }) {
    return CustomContainer(
      padding: padding ?? const EdgeInsets.all(AppConstants.spacing16),
      margin: margin,
      backgroundColor: backgroundColor ?? Colors.grey.shade50,
      borderRadius: borderRadius ?? AppConstants.borderRadius12,
      border: Border.all(
        color: borderColor ?? Colors.grey.shade300,
        width: 1,
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: alignment,
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        borderRadius: borderRadius != null 
            ? BorderRadius.circular(borderRadius!) 
            : null,
        border: border,
        boxShadow: boxShadow,
      ),
      child: child,
    );
  }
}
