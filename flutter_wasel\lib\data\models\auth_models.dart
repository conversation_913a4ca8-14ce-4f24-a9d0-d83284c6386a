// import 'package:json_annotation/json_annotation.dart';
import 'user_model.dart';

// part 'auth_models.g.dart';

class LoginRequest {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => LoginRequest(
    email: json['email'] as String,
    password: json['password'] as String,
  );

  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
  };
}

class SignupRequest {
  final String email;
  final String password;
  final String? role;
  final String? supplierId;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;

  const SignupRequest({
    required this.email,
    required this.password,
    this.role,
    this.supplierId,
    this.firstName,
    this.lastName,
    this.phoneNumber,
  });

  factory SignupRequest.fromJson(Map<String, dynamic> json) => SignupRequest(
    email: json['email'] as String,
    password: json['password'] as String,
    role: json['role'] as String?,
    supplierId: json['supplierId'] as String?,
    firstName: json['firstName'] as String?,
    lastName: json['lastName'] as String?,
    phoneNumber: json['phoneNumber'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
    if (role != null) 'role': role,
    if (supplierId != null) 'supplierId': supplierId,
    if (firstName != null) 'firstName': firstName,
    if (lastName != null) 'lastName': lastName,
    if (phoneNumber != null) 'phoneNumber': phoneNumber,
  };
}

class ForgotPasswordRequest {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  factory ForgotPasswordRequest.fromJson(Map<String, dynamic> json) => ForgotPasswordRequest(
    email: json['email'] as String,
  );

  Map<String, dynamic> toJson() => {
    'email': email,
  };
}

class ResetPasswordRequest {
  final String token;
  final String password;

  const ResetPasswordRequest({
    required this.token,
    required this.password,
  });

  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) => ResetPasswordRequest(
    token: json['token'] as String,
    password: json['password'] as String,
  );

  Map<String, dynamic> toJson() => {
    'token': token,
    'password': password,
  };
}

class RefreshTokenRequest {
  final String refreshToken;

  const RefreshTokenRequest({
    required this.refreshToken,
  });

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) => RefreshTokenRequest(
    refreshToken: json['refreshToken'] as String,
  );

  Map<String, dynamic> toJson() => {
    'refreshToken': refreshToken,
  };
}

class AuthResponse {
  final bool success;
  final String message;
  final AuthData? data;
  final String? error;

  const AuthResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) => AuthResponse(
    success: json['success'] as bool,
    message: json['message'] as String,
    data: json['data'] != null ? AuthData.fromJson(json['data'] as Map<String, dynamic>) : null,
    error: json['error'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    if (data != null) 'data': data!.toJson(),
    if (error != null) 'error': error,
  };
}

class AuthData {
  final UserModel user;
  final String accessToken;
  final String refreshToken;

  const AuthData({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) => AuthData(
    user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
    accessToken: json['accessToken'] as String,
    refreshToken: json['refreshToken'] as String,
  );

  Map<String, dynamic> toJson() => {
    'user': user.toJson(),
    'accessToken': accessToken,
    'refreshToken': refreshToken,
  };
}

class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final String? error;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.error,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      error: json['error'] as String?,
    );
  }

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) {
    return {
      'success': success,
      'message': message,
      'data': data != null ? toJsonT(data as T) : null,
      'error': error,
    };
  }
}
