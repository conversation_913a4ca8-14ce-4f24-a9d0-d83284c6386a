import { useState } from 'react';
import { Dimensions, Pressable } from 'react-native';
import { View, Text, Card, Button, YStack } from 'tamagui';
import { Mo<PERSON>View } from 'moti';
import { suppliersData } from '~/temp-data/suppliersData';
import { router } from 'expo-router';
import { Image } from 'react-native';
import MapView, { Marker } from 'react-native-maps';

type Addition = { id: string; name: string; price: number }

type Product = {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  }

type Supplier = {
        id: string;
        name: string;
        lat: number;
        lng: number;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Product[];
    };

export default function SuppliersMap() {
  const { width, height } = Dimensions.get('window');
  const [selected, setSelected] = useState<Supplier | null>(null);

  // Default region for the map (Amman, Jordan)
  const defaultRegion = {
    latitude: 31.9539,
    longitude: 35.9106,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  return (
    <View flex={1}>
      {/* Real Map */}
      <MapView
        style={{ width, height }}
        initialRegion={defaultRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {/* Supplier Markers */}
        {suppliersData.map((sup, index) => (
          <Marker
            key={sup.id}
            coordinate={{
              latitude: sup.lat,
              longitude: sup.lng,
            }}
            onPress={() => setSelected(sup)}
          >
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                overflow: 'hidden',
                borderWidth: 2,
                borderColor: '#fff',
                backgroundColor: '#eee',
                justifyContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 5,
              }}
            >
              <Image
                source={{ uri: sup.logoUrl }}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
              />
            </View>
          </Marker>
        ))}
      </MapView>

      {selected && (
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing' }}
          style={{ position: 'absolute', bottom: 80, left: 20, right: 20 }}
        >
          <Card p="$4" br="$6" elevate bw={1} bc="$gray5" bg="white">
            <YStack gap="$2">
              <Text fontSize="$6" fontWeight="700">{selected.name}</Text>
              <Text fontSize="$4" color="$gray9">{selected.category}</Text>
              <Button onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: selected.id }
              })}>
                View
              </Button>
              <Button variant="outlined" onPress={() => setSelected(null)}>Close</Button>
            </YStack>
          </Card>
        </MotiView>
      )}
    </View>
  );
}
