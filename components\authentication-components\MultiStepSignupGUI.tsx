import React, { useState } from 'react';
import { YStack, XStack, H4, Text, Progress, Theme } from 'tamagui';
import { ScrollView, Dimensions, Alert } from 'react-native';
import { useSignupStore } from './useSignupStore';
import { Button } from '~/components/Button';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useRouter } from 'expo-router';

// Step Components
import { BasicInfoStep } from './signup-steps/BasicInfoStep';
import { ContactSecurityStep } from './signup-steps/ContactSecurityStep';
import { ProfileSetupStep } from './signup-steps/ProfileSetupStep';
import { AddressInfoStep } from './signup-steps/AddressInfoStep';
import { BusinessInfoStep } from './signup-steps/BusinessInfoStep';
import { FinalStep } from './signup-steps/FinalStep';

export const MultiStepSignupGUI = () => {
  const {
    currentStep,
    totalSteps,
    signupData,
    nextStep,
    prevStep,
    isStepValid,
    resetSignup,
    submitSignup,
    isSubmitting
  } = useSignupStore();

  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  const { width } = Dimensions.get('window');
  const progress = (currentStep / totalSteps) * 100;

  // Skip business step for customers
  const shouldShowBusinessStep = signupData.userType === 'supplier';
  const effectiveTotalSteps = shouldShowBusinessStep ? totalSteps : totalSteps - 1;
  const effectiveProgress = shouldShowBusinessStep 
    ? progress 
    : currentStep <= 4 
      ? (currentStep / (totalSteps - 1)) * 100
      : ((currentStep - 1) / (totalSteps - 1)) * 100;

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Basic Information';
      case 2: return 'Contact & Security';
      case 3: return 'Profile Setup';
      case 4: return 'Address Information';
      case 5: return shouldShowBusinessStep ? 'Business Information' : 'Final Step';
      case 6: return 'Final Step';
      default: return 'Sign Up';
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep />;
      case 2:
        return <ContactSecurityStep />;
      case 3:
        return <ProfileSetupStep />;
      case 4:
        return <AddressInfoStep />;
      case 5:
        return shouldShowBusinessStep ? <BusinessInfoStep /> : <FinalStep />;
      case 6:
        return <FinalStep />;
      default:
        return <BasicInfoStep />;
    }
  };

  const handleNext = async () => {
    console.log('handleNext called, currentStep:', currentStep, 'isLastStep:', isLastStep, 'shouldShowBusinessStep:', shouldShowBusinessStep);

    if (!isStepValid(currentStep)) {
      console.log('Step is not valid:', currentStep);
      return;
    }

    // If this is the last step, submit the form
    if (isLastStep) {
      console.log('Submitting signup form...');
      setIsProcessing(true);

      try {
        console.log('Calling submitSignup...');
        const result = await submitSignup();
        console.log('Signup result:', result);

        if (result && result.success) {
          // Navigate to email verification page
          resetSignup();
          router.push({
            pathname: '/authentication/email-verification',
            params: { email: signupData.email }
          });
        } else {
          Alert.alert('Signup Failed', result?.message || 'Failed to create account. Please try again.');
        }
      } catch (error) {
        console.error('Signup error:', error);
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      } finally {
        setIsProcessing(false);
      }
    } else {
      console.log('Moving to next step...');
      nextStep();
    }
  };

  const handlePrev = () => {
    if (currentStep === 6 && !shouldShowBusinessStep) {
      // Skip business step when going back for customers
      prevStep();
      prevStep();
    } else {
      prevStep();
    }
  };

  const canGoNext = isStepValid(currentStep);
  const isLastStep = currentStep === (shouldShowBusinessStep ? totalSteps : totalSteps - 1);

  return (
    <Theme name="light">
      <YStack flex={1} backgroundColor="$background" width="100%">
        {/* Header with Progress */}
        <YStack padding="$4" paddingTop="$6" backgroundColor="$primary" borderBottomLeftRadius="$6" borderBottomRightRadius="$6">
          <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
            <H4 color="white" fontWeight="bold">{getStepTitle()}</H4>
            <XStack alignItems="center" gap="$3">
              <Text color="white" fontSize="$3">
                {currentStep}/{shouldShowBusinessStep ? totalSteps : totalSteps - 1}
              </Text>
              <Button
                size="$2"
                backgroundColor="transparent"
                color="white"
                onPress={() => {
                  console.log('Navigating to login...');
                  router.push('/authentication/login');
                }}
                paddingHorizontal="$3"
                paddingVertical="$2"
                borderColor="white"
                borderWidth={1}
                borderRadius="$3"
                pressStyle={{
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  borderColor: 'white'
                }}
              >
                <Text color="white" fontSize="$3" fontWeight="500">
                  Login
                </Text>
              </Button>
            </XStack>
          </XStack>
          
          <Progress 
            value={effectiveProgress} 
            backgroundColor="rgba(255,255,255,0.3)"
            height={6}
            borderRadius="$2"
          >
            <Progress.Indicator 
              backgroundColor="white" 
              borderRadius="$2"
              animation="bouncy"
            />
          </Progress>
        </YStack>

        {/* Step Content */}
        <ScrollView 
          contentContainerStyle={{ 
            flexGrow: 1, 
            padding: 20,
            paddingBottom: 120 
          }}
          showsVerticalScrollIndicator={false}
        >
          <MotiView
            key={currentStep}
            from={{ opacity: 0, translateX: 50 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ type: 'timing', duration: 300 }}
          >
            {renderCurrentStep()}
          </MotiView>
        </ScrollView>

        {/* Navigation Buttons */}
        <YStack 
          position="absolute" 
          bottom={0} 
          left={0} 
          right={0} 
          backgroundColor="white" 
          padding="$4"
          borderTopWidth={1}
          borderTopColor="$borderColor"
          shadowColor="$shadowColor"
          shadowOffset={{ width: 0, height: -2 }}
          shadowOpacity={0.1}
          shadowRadius={4}
          elevation={5}
        >
          {/* Sign In Link */}
          <XStack alignItems="center" justifyContent="center" marginBottom="$3">
            <Text fontSize="$3" color="$gray9">
              Already have an account?{' '}
            </Text>
            <Button
              variant="ghost"
              size="$3"
              backgroundColor="transparent"
              onPress={() => {
                console.log('Bottom Login button pressed');
                router.push('/authentication/login');
              }}
              paddingHorizontal="$2"
              paddingVertical="$1"
              pressStyle={{
                backgroundColor: '$primary',
                opacity: 0.1
              }}
            >
              <Text color="$primary" fontSize="$3" fontWeight="600">
                Login
              </Text>
            </Button>
          </XStack>

          <XStack gap="$3" justifyContent="space-between">
            {currentStep > 1 ? (
              <Button
                flex={1}
                backgroundColor="$gray5"
                color="$gray11"
                onPress={handlePrev}
                icon={<Ionicons name="chevron-back" size={20} color="#666" />}
              >
                Back
              </Button>
            ) : (
              <Button
                flex={1}
                backgroundColor="$gray3"
                color="$gray8"
                onPress={resetSignup}
              >
                Reset
              </Button>
            )}

            <Button
              flex={2}
              backgroundColor={canGoNext ? "$primary" : "$gray5"}
              color={canGoNext ? "white" : "$gray8"}
              disabled={!canGoNext || isProcessing || isSubmitting}
              onPress={handleNext}
              icon={
                isProcessing || isSubmitting ? (
                  <Ionicons name="hourglass" size={20} color="white" />
                ) : isLastStep ? (
                  <Ionicons name="checkmark" size={20} color="white" />
                ) : (
                  <Ionicons name="chevron-forward" size={20} color="white" />
                )
              }
            >
              {isProcessing || isSubmitting ? 'Creating Account...' : isLastStep ? 'Complete' : 'Next'}
            </Button>
          </XStack>
        </YStack>
      </YStack>
    </Theme>
  );
};
