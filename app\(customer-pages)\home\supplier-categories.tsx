import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { CustomerScreenContent } from '~/components/customer-pages-components/CustomerScreenContent';

export default function SupplierCategories() {
  return (
    <>
        <Stack.Screen options={{ title: 'Supplier Categories', headerShown: true }} />
        <Container alignSelf='center' alignItems="center" justifyContent="center" style={{ width: '100%', padding: 0 }}>
          <CustomerScreenContent path="app/(customer-pages)/home/<USER>" title="SupplierCategories"></CustomerScreenContent>
        </Container>
    </>
  );
}