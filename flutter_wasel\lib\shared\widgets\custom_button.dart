import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';

enum ButtonVariant { primary, secondary, outline, text }
enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderRadius,
    this.padding,
  });

  // Factory constructors for common button types
  factory CustomButton.primary({
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? icon,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      variant: ButtonVariant.primary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      icon: icon,
    );
  }

  factory CustomButton.secondary({
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? icon,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      variant: ButtonVariant.secondary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      icon: icon,
    );
  }

  factory CustomButton.outline({
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? icon,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      variant: ButtonVariant.outline,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
      icon: icon,
    );
  }

  factory CustomButton.text({
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
    Widget? icon,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      variant: ButtonVariant.text,
      size: size,
      isLoading: isLoading,
      isFullWidth: false,
      icon: icon,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDisabled = onPressed == null || isLoading;

    // Get size properties
    final sizeProps = _getSizeProperties();
    
    // Get variant properties
    final variantProps = _getVariantProperties(theme);

    Widget buttonChild = Row(
      mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading) ...[
          SizedBox(
            width: sizeProps.iconSize,
            height: sizeProps.iconSize,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                variantProps.foregroundColor,
              ),
            ),
          ),
          const SizedBox(width: AppConstants.spacing8),
        ] else if (icon != null) ...[
          icon!,
          const SizedBox(width: AppConstants.spacing8),
        ],
        Text(
          text,
          style: sizeProps.textStyle.copyWith(
            color: variantProps.foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );

    Widget button;

    switch (variant) {
      case ButtonVariant.primary:
      case ButtonVariant.secondary:
        button = ElevatedButton(
          onPressed: isDisabled ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: variantProps.backgroundColor,
            foregroundColor: variantProps.foregroundColor,
            disabledBackgroundColor: variantProps.backgroundColor.withOpacity(0.5),
            disabledForegroundColor: variantProps.foregroundColor.withOpacity(0.5),
            elevation: variant == ButtonVariant.primary ? 2 : 0,
            shadowColor: variant == ButtonVariant.primary 
                ? variantProps.backgroundColor.withOpacity(0.3) 
                : null,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                borderRadius ?? AppConstants.borderRadius12,
              ),
            ),
            padding: padding ?? sizeProps.padding,
          ),
          child: buttonChild,
        );
        break;

      case ButtonVariant.outline:
        button = OutlinedButton(
          onPressed: isDisabled ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: variantProps.foregroundColor,
            disabledForegroundColor: variantProps.foregroundColor.withOpacity(0.5),
            side: BorderSide(
              color: borderColor ?? variantProps.foregroundColor,
              width: 1.5,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                borderRadius ?? AppConstants.borderRadius12,
              ),
            ),
            padding: padding ?? sizeProps.padding,
          ),
          child: buttonChild,
        );
        break;

      case ButtonVariant.text:
        button = TextButton(
          onPressed: isDisabled ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: variantProps.foregroundColor,
            disabledForegroundColor: variantProps.foregroundColor.withOpacity(0.5),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                borderRadius ?? AppConstants.borderRadius12,
              ),
            ),
            padding: padding ?? sizeProps.padding,
          ),
          child: buttonChild,
        );
        break;
    }

    if (isFullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  _ButtonSizeProperties _getSizeProperties() {
    switch (size) {
      case ButtonSize.small:
        return _ButtonSizeProperties(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.spacing16,
            vertical: AppConstants.spacing8,
          ),
          textStyle: const TextStyle(fontSize: 14),
          iconSize: 16,
        );
      case ButtonSize.medium:
        return _ButtonSizeProperties(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.spacing24,
            vertical: AppConstants.spacing16,
          ),
          textStyle: const TextStyle(fontSize: 16),
          iconSize: 20,
        );
      case ButtonSize.large:
        return _ButtonSizeProperties(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.spacing32,
            vertical: AppConstants.spacing20,
          ),
          textStyle: const TextStyle(fontSize: 18),
          iconSize: 24,
        );
    }
  }

  _ButtonVariantProperties _getVariantProperties(ThemeData theme) {
    switch (variant) {
      case ButtonVariant.primary:
        return _ButtonVariantProperties(
          backgroundColor: backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: foregroundColor ?? theme.colorScheme.onPrimary,
        );
      case ButtonVariant.secondary:
        return _ButtonVariantProperties(
          backgroundColor: backgroundColor ?? theme.colorScheme.secondary,
          foregroundColor: foregroundColor ?? theme.colorScheme.onSecondary,
        );
      case ButtonVariant.outline:
      case ButtonVariant.text:
        return _ButtonVariantProperties(
          backgroundColor: backgroundColor ?? Colors.transparent,
          foregroundColor: foregroundColor ?? theme.colorScheme.primary,
        );
    }
  }
}

class _ButtonSizeProperties {
  final EdgeInsetsGeometry padding;
  final TextStyle textStyle;
  final double iconSize;

  _ButtonSizeProperties({
    required this.padding,
    required this.textStyle,
    required this.iconSize,
  });
}

class _ButtonVariantProperties {
  final Color backgroundColor;
  final Color foregroundColor;

  _ButtonVariantProperties({
    required this.backgroundColor,
    required this.foregroundColor,
  });
}
