import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../presentation/pages/auth/login_page.dart';
import '../../presentation/pages/auth/signup_page.dart';
import '../../presentation/pages/auth/forgot_password_page.dart';
import '../../presentation/pages/auth/reset_password_page.dart';
import '../../presentation/pages/customer/customer_main_page.dart';
import '../../presentation/pages/customer/customer_home_page.dart';
import '../../presentation/pages/customer/customer_orders_page.dart';
import '../../presentation/pages/customer/customer_packages_page.dart';
import '../../presentation/pages/customer/customer_profile_page.dart';
import '../../presentation/pages/customer/suppliers_map_page.dart';
import '../../presentation/pages/supplier/supplier_main_page.dart';
import '../../presentation/pages/supplier/supplier_home_page.dart';
import '../../presentation/pages/supplier/supplier_profile_page.dart';
import '../../presentation/providers/auth_provider.dart';

// Route names
class AppRoutes {
  // Auth routes
  static const String login = '/login';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  
  // Customer routes
  static const String customerMain = '/customer';
  static const String customerHome = '/customer/home';
  static const String customerOrders = '/customer/orders';
  static const String customerPackages = '/customer/packages';
  static const String customerProfile = '/customer/profile';
  static const String suppliersMap = '/customer/suppliers-map';
  
  // Supplier routes
  static const String supplierMain = '/supplier';
  static const String supplierHome = '/supplier/home';
  static const String supplierProfile = '/supplier/profile';
}

final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  
  return GoRouter(
    initialLocation: authState.isAuthenticated 
        ? (authState.user?.isCustomer == true ? AppRoutes.customerMain : AppRoutes.supplierMain)
        : AppRoutes.login,
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final user = authState.user;
      final isLoading = authState.isLoading;
      
      // Show loading screen while checking auth
      if (isLoading) {
        return null;
      }
      
      // Auth routes - redirect if already authenticated
      final authRoutes = [
        AppRoutes.login,
        AppRoutes.signup,
        AppRoutes.forgotPassword,
        AppRoutes.resetPassword,
      ];
      
      if (authRoutes.contains(state.matchedLocation) && isAuthenticated) {
        return user?.isCustomer == true ? AppRoutes.customerMain : AppRoutes.supplierMain;
      }
      
      // Protected routes - redirect to login if not authenticated
      if (!isAuthenticated && !authRoutes.contains(state.matchedLocation)) {
        return AppRoutes.login;
      }
      
      // Role-based routing
      if (isAuthenticated && user != null) {
        final location = state.matchedLocation;
        
        // Customer trying to access supplier routes
        if (user.isCustomer && location.startsWith('/supplier')) {
          return AppRoutes.customerMain;
        }
        
        // Supplier trying to access customer routes
        if (user.isSupplier && location.startsWith('/customer')) {
          return AppRoutes.supplierMain;
        }
      }
      
      return null;
    },
    routes: [
      // Auth routes
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: AppRoutes.signup,
        builder: (context, state) => const SignupPage(),
      ),
      GoRoute(
        path: AppRoutes.forgotPassword,
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      GoRoute(
        path: AppRoutes.resetPassword,
        builder: (context, state) {
          final token = state.uri.queryParameters['token'] ?? '';
          return ResetPasswordPage(token: token);
        },
      ),
      
      // Customer routes
      ShellRoute(
        builder: (context, state, child) => CustomerMainPage(child: child),
        routes: [
          GoRoute(
            path: AppRoutes.customerMain,
            redirect: (context, state) => AppRoutes.customerHome,
          ),
          GoRoute(
            path: AppRoutes.customerHome,
            builder: (context, state) => const CustomerHomePage(),
          ),
          GoRoute(
            path: AppRoutes.customerOrders,
            builder: (context, state) => const CustomerOrdersPage(),
          ),
          GoRoute(
            path: AppRoutes.customerPackages,
            builder: (context, state) => const CustomerPackagesPage(),
          ),
          GoRoute(
            path: AppRoutes.customerProfile,
            builder: (context, state) => const CustomerProfilePage(),
          ),
          GoRoute(
            path: AppRoutes.suppliersMap,
            builder: (context, state) => const SuppliersMapPage(),
          ),
        ],
      ),
      
      // Supplier routes
      ShellRoute(
        builder: (context, state, child) => SupplierMainPage(child: child),
        routes: [
          GoRoute(
            path: AppRoutes.supplierMain,
            redirect: (context, state) => AppRoutes.supplierHome,
          ),
          GoRoute(
            path: AppRoutes.supplierHome,
            builder: (context, state) => const SupplierHomePage(),
          ),
          GoRoute(
            path: AppRoutes.supplierProfile,
            builder: (context, state) => const SupplierProfilePage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.login),
              child: const Text('Go to Login'),
            ),
          ],
        ),
      ),
    ),
  );
});
