import { useState } from 'react'
import { Image, ScrollView, Dimensions, Pressable } from 'react-native'
import { YStack, XStack, Text, H4, H6, Input, Button, Card, Label, Separator, Switch, Sheet } from 'tamagui'
import { Ionicons } from '@expo/vector-icons'
import MapboxGL from '@rnmapbox/maps'
import * as Location from 'expo-location';
import { useCurrentUserData } from '~/components/useCurrentUserData'
import { suppliersData } from '~/temp-data/suppliersData'
import * as ImagePicker from 'expo-image-picker'
import { Modal, Platform } from 'react-native'

MapboxGL.setAccessToken('pk.eyJ1Ijoib21hci1qYXJib3UyMDA0IiwiYSI6ImNtY2dlcjF0YTBoNGQybnF2YXczcTZjM3oifQ.bicTczx8tplFAF9fSASLTw')

export default function SupplierProfile() {
  const { user } = useCurrentUserData();
  const supplierData = suppliersData.find((s) => s.id === user?.supplierId);
  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState(supplierData?.phone || '');
  const [storeName, setStoreName] = useState(supplierData?.name || '');
  const [location, setLocation] = useState<[number, number]>(
    supplierData?.lng && supplierData?.lat
      ? [supplierData.lng, supplierData.lat]
      : [35.2137, 31.7683]
  );
  const [bannerUri, setBannerUri] = useState(supplierData?.banner);
  const [logoUri, setLogoUri] = useState(supplierData?.logoUrl);
  const [openHours, setOpenHours] = useState(supplierData?.openHours || '');
  const [mapFullscreen, setMapFullscreen] = useState(false);
  const [storeOpen, setStoreOpen] = useState(true);
  const [showFullMap, setShowFullMap] = useState(false);

  const windowWidth = Dimensions.get('window').width;

  async function pickBannerImage() {
    const res = await ImagePicker.launchImageLibraryAsync({ mediaTypes: ImagePicker.MediaTypeOptions.Images });
    if (!res.canceled && res.assets[0].uri) setBannerUri(res.assets[0].uri);
  }

  async function pickLogoImage() {
    const res = await ImagePicker.launchImageLibraryAsync({ mediaTypes: ImagePicker.MediaTypeOptions.Images });
    if (!res.canceled && res.assets[0].uri) setLogoUri(res.assets[0].uri);
  }


  return (
    <>
      <ScrollView contentContainerStyle={{ paddingBottom: 120, width: windowWidth }}>
        {/* Cover Image with Change Option */}
        <Pressable onPress={pickBannerImage}>
            <Image
                source={{ uri: bannerUri }}
                style={{ width: '100%', height: 180 }}
                resizeMode="cover"
            />
            <Text
                style={{
                position: 'absolute',
                right: 10,
                top: 10,
                color: 'white',
                backgroundColor: '#00000080',
                padding: 6,
                borderRadius: 6,
                }}
            >
                Change Cover
            </Text>
        </Pressable>

        {/* Profile Picture with Edit */}
        <XStack jc="center" mt={-50}>
            <Pressable onPress={pickLogoImage}>
                <Image
                source={{ uri: logoUri }}
                style={{
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    borderWidth: 3,
                    borderColor: 'white',
                    backgroundColor: '#f1f5f9',
                }}
                />
                <Text
                style={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    backgroundColor: '#00000080',
                    color: 'white',
                    fontSize: 12,
                    paddingHorizontal: 6,
                    borderRadius: 6,
                }}
                >
                Edit
                </Text>
            </Pressable>
        </XStack>

        <H4 ta="center" color="$primary">{storeName || 'Unnamed Store'}</H4>

        {/* Info Section */}
        <YStack gap="$4" p="$4">
          <Card gap="$3" p="$4" elevate br="$8">
            <Label>Email</Label>
            <Input value={email} onChangeText={setEmail} autoCapitalize="none" keyboardType="email-address" />

            <Label>Phone</Label>
            <Input value={phone} onChangeText={setPhone} keyboardType="phone-pad" />

            <Label>Store Name</Label>
            <Input value={storeName} onChangeText={setStoreName} />

            <Label>Open Hours</Label>
            <Input value={openHours} onChangeText={setOpenHours} placeholder="e.g. 9:00 AM – 11:00 PM" />

            <XStack ai="center" jc="space-between" mt="$3">
              <Label>Store Open</Label>
              <Switch
                checked={storeOpen}
                onCheckedChange={setStoreOpen}
                backgroundColor={storeOpen ? '$green8' : '$red7'}
              />
            </XStack>
          </Card>

          <Separator />

          {/* Map Section */}
          <H6>Store Location</H6>
          <Pressable onPress={() => setMapFullscreen(true)}>
            <Card elevate br="$6" height={300} overflow="hidden">
                <MapboxGL.MapView
                    style={{ flex: 1 }}
                    scrollEnabled={false}
                    zoomEnabled={false}
                    rotateEnabled={false}
                    pitchEnabled={false}
                    >
                    <MapboxGL.Camera centerCoordinate={location} zoomLevel={13} />
                    <MapboxGL.PointAnnotation id="store" coordinate={location}>
                        <></>
                    </MapboxGL.PointAnnotation>
                </MapboxGL.MapView>
            </Card>
            </Pressable>


          <Button
            mt="$5"
            br="$6"
            icon={<Ionicons name="save-outline" size={20} />}
            backgroundColor="$primary"
            color="white"
          >
            Save Changes
          </Button>
        </YStack>
      </ScrollView>

      {/* Full Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide">
        <YStack f={1}>
            <MapboxGL.MapView style={{ flex: 1 }} onPress={(e) => {
                if (e.geometry.type === 'Point' && Array.isArray(e.geometry.coordinates)) {
                    const coords = e.geometry.coordinates;
                    if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                        setLocation([coords[0], coords[1]]);
                    }
                }
            }}>
                <MapboxGL.Camera centerCoordinate={location} zoomLevel={14} />
                <MapboxGL.PointAnnotation id="marker" coordinate={location}>
                    <></>
                </MapboxGL.PointAnnotation>
            </MapboxGL.MapView>
            <Button
            bg="$primary"
            color="white"
            br={100}
            position="absolute"
            top={40}
            right={20}
            zIndex={1000}
            icon={<Ionicons name="close" size={20} color="white" />}
            onPress={() => setMapFullscreen(false)}
            />
        </YStack>
        </Modal>
    </>
  )
}
