import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/constants/app_constants.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';
import '../services/http_service.dart';
import '../services/storage_service.dart';

class AuthRepository {
  final HttpService _httpService;
  final StorageService _storageService;

  AuthRepository(this._httpService, this._storageService);

  // Login
  Future<AuthResponse> login(LoginRequest request) async {
    try {
      final response = await _httpService.post(
        '${AppConstants.authEndpoint}/login',
        data: request.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.data != null) {
        // Save tokens and user data
        await _storageService.saveAccessToken(authResponse.data!.accessToken);
        await _storageService.saveRefreshToken(authResponse.data!.refreshToken);
        await _storageService.saveUserData(authResponse.data!.user);
        await _storageService.setLoggedIn(true);
      }

      return authResponse;
    } on DioException catch (e) {
      if (e.response != null) {
        return AuthResponse.fromJson(e.response!.data);
      }
      return AuthResponse(
        success: false,
        message: 'Network error: ${e.message}',
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Unexpected error: $e',
      );
    }
  }

  // Signup
  Future<AuthResponse> signup(SignupRequest request) async {
    try {
      final response = await _httpService.post(
        '${AppConstants.authEndpoint}/signup',
        data: request.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.data != null) {
        // Save tokens and user data
        await _storageService.saveAccessToken(authResponse.data!.accessToken);
        await _storageService.saveRefreshToken(authResponse.data!.refreshToken);
        await _storageService.saveUserData(authResponse.data!.user);
        await _storageService.setLoggedIn(true);
      }

      return authResponse;
    } on DioException catch (e) {
      if (e.response != null) {
        return AuthResponse.fromJson(e.response!.data);
      }
      return AuthResponse(
        success: false,
        message: 'Network error: ${e.message}',
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Unexpected error: $e',
      );
    }
  }

  // Forgot Password
  Future<ApiResponse<void>> forgotPassword(ForgotPasswordRequest request) async {
    try {
      final response = await _httpService.post(
        '${AppConstants.authEndpoint}/forgot-password',
        data: request.toJson(),
      );

      return ApiResponse<void>.fromJson(response.data, (json) => null);
    } on DioException catch (e) {
      if (e.response != null) {
        return ApiResponse<void>.fromJson(e.response!.data, (json) => null);
      }
      return ApiResponse<void>(
        success: false,
        message: 'Network error: ${e.message}',
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        message: 'Unexpected error: $e',
      );
    }
  }

  // Reset Password
  Future<ApiResponse<void>> resetPassword(ResetPasswordRequest request) async {
    try {
      final response = await _httpService.post(
        '${AppConstants.authEndpoint}/reset-password',
        data: request.toJson(),
      );

      return ApiResponse<void>.fromJson(response.data, (json) => null);
    } on DioException catch (e) {
      if (e.response != null) {
        return ApiResponse<void>.fromJson(e.response!.data, (json) => null);
      }
      return ApiResponse<void>(
        success: false,
        message: 'Network error: ${e.message}',
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        message: 'Unexpected error: $e',
      );
    }
  }

  // Refresh Token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _httpService.post(
        '${AppConstants.authEndpoint}/refresh-token',
        data: RefreshTokenRequest(refreshToken: refreshToken).toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.data != null) {
        await _storageService.saveAccessToken(authResponse.data!.accessToken);
        await _storageService.saveRefreshToken(authResponse.data!.refreshToken);
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      // Try to logout from server
      await _httpService.post('${AppConstants.authEndpoint}/logout');
    } catch (e) {
      // Continue with local logout even if server request fails
    } finally {
      // Clear local data
      await _storageService.clearAll();
    }
  }

  // Logout from all devices
  Future<void> logoutAll() async {
    try {
      // Try to logout from server
      await _httpService.post('${AppConstants.authEndpoint}/logout-all');
    } catch (e) {
      // Continue with local logout even if server request fails
    } finally {
      // Clear local data
      await _storageService.clearAll();
    }
  }

  // Get current user
  Future<UserModel?> getCurrentUser() async {
    return await _storageService.getUserData();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final isLoggedIn = await _storageService.isLoggedIn();
    final accessToken = await _storageService.getAccessToken();
    return isLoggedIn && accessToken != null;
  }

  // Get access token
  Future<String?> getAccessToken() async {
    return await _storageService.getAccessToken();
  }

  // Get refresh token
  Future<String?> getRefreshToken() async {
    return await _storageService.getRefreshToken();
  }
}

// Provider for AuthRepository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final httpService = ref.read(httpServiceProvider);
  final storageService = ref.read(storageServiceProvider);
  return AuthRepository(httpService, storageService);
});
