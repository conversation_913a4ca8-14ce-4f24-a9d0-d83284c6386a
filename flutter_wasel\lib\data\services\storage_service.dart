import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/constants/app_constants.dart';
import '../models/user_model.dart';

class StorageService {
  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // Token management
  Future<void> saveAccessToken(String token) async {
    await prefs.setString(AppConstants.accessTokenKey, token);
  }

  Future<String?> getAccessToken() async {
    return prefs.getString(AppConstants.accessTokenKey);
  }

  Future<void> saveRefreshToken(String token) async {
    await prefs.setString(AppConstants.refreshTokenKey, token);
  }

  Future<String?> getRefreshToken() async {
    return prefs.getString(AppConstants.refreshTokenKey);
  }

  Future<void> clearTokens() async {
    await prefs.remove(AppConstants.accessTokenKey);
    await prefs.remove(AppConstants.refreshTokenKey);
  }

  // User data management
  Future<void> saveUserData(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await prefs.setString(AppConstants.userDataKey, userJson);
  }

  Future<UserModel?> getUserData() async {
    final userJson = prefs.getString(AppConstants.userDataKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      } catch (e) {
        // If parsing fails, remove corrupted data
        await clearUserData();
        return null;
      }
    }
    return null;
  }

  Future<void> clearUserData() async {
    await prefs.remove(AppConstants.userDataKey);
  }

  // Login state management
  Future<void> setLoggedIn(bool isLoggedIn) async {
    await prefs.setBool(AppConstants.isLoggedInKey, isLoggedIn);
  }

  Future<bool> isLoggedIn() async {
    return prefs.getBool(AppConstants.isLoggedInKey) ?? false;
  }

  // Clear all data (logout)
  Future<void> clearAll() async {
    await clearTokens();
    await clearUserData();
    await setLoggedIn(false);
  }

  // Generic methods for other data
  Future<void> setString(String key, String value) async {
    await prefs.setString(key, value);
  }

  String? getString(String key) {
    return prefs.getString(key);
  }

  Future<void> setBool(String key, bool value) async {
    await prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return prefs.getBool(key);
  }

  Future<void> setInt(String key, int value) async {
    await prefs.setInt(key, value);
  }

  int? getInt(String key) {
    return prefs.getInt(key);
  }

  Future<void> setDouble(String key, double value) async {
    await prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    return prefs.getDouble(key);
  }

  Future<void> setStringList(String key, List<String> value) async {
    await prefs.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return prefs.getStringList(key);
  }

  Future<void> remove(String key) async {
    await prefs.remove(key);
  }

  bool containsKey(String key) {
    return prefs.containsKey(key);
  }

  Set<String> getKeys() {
    return prefs.getKeys();
  }
}

// Provider for StorageService
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

// Provider for initialized StorageService
final initializedStorageServiceProvider = FutureProvider<StorageService>((ref) async {
  final storage = ref.read(storageServiceProvider);
  await storage.init();
  return storage;
});
