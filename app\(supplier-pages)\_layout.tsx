import { Tabs } from 'expo-router'
import { Ionicons } from '@expo/vector-icons'
import { View } from 'tamagui'
import { Pressable } from 'react-native'

export default function SupplierTabsLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#7529B3',
        tabBarStyle: {
          backgroundColor: '#fff',
          height: 60,
          elevation: 5
      },
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          headerShown: false,
          title: 'Home',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused? "home" : "home-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
              name="profile"
              options={{
                headerShown: false,
                title: 'Profile',
                tabBarIcon: ({ color, size, focused }) => (
                  <Ionicons name={focused? "person" : "person-outline"} size={size} color={color} />
                ),
              }}
      />
    </Tabs>
  )
}
