import { Response } from 'express';
import User from '../models/User';
import { JWTUtils } from '../utils/jwt';
import { CryptoUtils } from '../utils/crypto';
import { EmailService } from '../services/emailService';
import {
  AuthenticatedRequest,
  LoginRequest,
  SignupRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  RefreshTokenRequest,
  ApiResponse
} from '../types';

export class AuthController {
  static async signup(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        username,
        dateOfBirth,
        gender,
        address,
        city,
        country,
        role,
        supplierId,
        storeName,
        businessType,
        openHours,
        location,
        notifications = true
      }: SignupRequest = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });
      if (existingUser) {
        const field = existingUser.email === email ? 'email' : 'username';
        res.status(400).json({
          success: false,
          message: `User with this ${field} already exists`,
        });
        return;
      }

      // Check if supplier ID is already taken (for suppliers)
      if (role === 'supplier' && supplierId) {
        const existingSupplier = await User.findOne({ supplierId });
        if (existingSupplier) {
          res.status(400).json({
            success: false,
            message: 'Supplier ID is already taken',
          });
          return;
        }
      }

      // Generate supplier ID if not provided
      let finalSupplierId = supplierId;
      if (role === 'supplier' && !supplierId) {
        finalSupplierId = `SUP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Create new user
      const userData: any = {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        username,
        dateOfBirth,
        gender,
        address,
        city,
        country,
        role,
        notifications,
        emailVerificationCode: CryptoUtils.generateOTP(6),
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };

      // Add supplier-specific fields
      if (role === 'supplier') {
        userData.supplierId = finalSupplierId;
        userData.storeName = storeName;
        userData.businessType = businessType;
        userData.openHours = openHours;
      }

      // Add location if provided
      if (location && Array.isArray(location) && location.length === 2) {
        userData.location = {
          type: 'Point',
          coordinates: location // [longitude, latitude]
        };
      }

      const user = new User(userData);
      await user.save();

      // Send email verification
      try {
        await EmailService.sendEmailVerification(user, user.emailVerificationCode!);
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Don't fail registration if email fails
      }

      res.status(201).json({
        success: true,
        message: 'User created successfully. Please check your email to verify your account.',
        data: {
          user: user.toJSON(),
          emailSent: true,
        },
      });
    } catch (error) {
      console.error('Signup error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async login(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { email, password }: LoginRequest = req.body;

      // Find user and include password for comparison
      const user = await User.findOne({ email }).select('+password');
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Invalid email or password',
        });
        return;
      }

      // Check if account is active
      if (!user.isActive) {
        res.status(401).json({
          success: false,
          message: 'Account is deactivated',
        });
        return;
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        res.status(401).json({
          success: false,
          message: 'Invalid email or password',
        });
        return;
      }

      // Generate tokens
      const tokenPayload = {
        userId: user._id,
        email: user.email,
        role: user.role,
      };

      const { accessToken, refreshToken } = JWTUtils.generateTokenPair(tokenPayload);

      // Save refresh token and update last login
      user.refreshTokens.push(refreshToken);
      user.lastLogin = new Date();
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          user: user.toJSON(),
          accessToken,
          refreshToken,
        },
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async refreshToken(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { refreshToken }: RefreshTokenRequest = req.body;

      if (!refreshToken) {
        res.status(400).json({
          success: false,
          message: 'Refresh token is required',
        });
        return;
      }

      // Verify refresh token
      const decoded = JWTUtils.verifyRefreshToken(refreshToken);

      // Find user and check if refresh token exists
      const user = await User.findById(decoded.userId);
      if (!user || !user.refreshTokens.includes(refreshToken)) {
        res.status(401).json({
          success: false,
          message: 'Invalid refresh token',
        });
        return;
      }

      // Check if account is active
      if (!user.isActive) {
        res.status(401).json({
          success: false,
          message: 'Account is deactivated',
        });
        return;
      }

      // Generate new tokens
      const tokenPayload = {
        userId: user._id,
        email: user.email,
        role: user.role,
      };

      const { accessToken, refreshToken: newRefreshToken } = JWTUtils.generateTokenPair(tokenPayload);

      // Replace old refresh token with new one
      user.refreshTokens = user.refreshTokens.filter(token => token !== refreshToken);
      user.refreshTokens.push(newRefreshToken);
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken,
          refreshToken: newRefreshToken,
        },
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token',
        error: error instanceof Error ? error.message : 'Token refresh failed',
      });
    }
  }

  static async logout(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { refreshToken } = req.body;
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Remove refresh token if provided
      if (refreshToken) {
        user.refreshTokens = user.refreshTokens.filter(token => token !== refreshToken);
        await user.save();
      }

      res.status(200).json({
        success: true,
        message: 'Logout successful',
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async logoutAll(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Clear all refresh tokens
      user.refreshTokens = [];
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Logged out from all devices successfully',
      });
    } catch (error) {
      console.error('Logout all error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async forgotPassword(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { email }: ForgotPasswordRequest = req.body;

      const user = await User.findOne({ email });
      if (!user) {
        // Don't reveal if user exists or not for security
        res.status(200).json({
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent',
        });
        return;
      }

      // Generate reset token
      const resetToken = CryptoUtils.generateRandomToken();
      user.passwordResetToken = CryptoUtils.hashToken(resetToken);
      user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      await user.save();

      // TODO: Send email with reset token
      // For now, we'll just return the token in development
      const responseData = process.env.NODE_ENV === 'development'
        ? { resetToken }
        : undefined;

      res.status(200).json({
        success: true,
        message: 'Password reset link has been sent to your email',
        data: responseData,
      });
    } catch (error) {
      console.error('Forgot password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async resetPassword(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { token, password }: ResetPasswordRequest = req.body;

      // Hash the token to compare with stored hash
      const hashedToken = CryptoUtils.hashToken(token);

      const user = await User.findOne({
        passwordResetToken: hashedToken,
        passwordResetExpires: { $gt: new Date() },
      });

      if (!user) {
        res.status(400).json({
          success: false,
          message: 'Invalid or expired reset token',
        });
        return;
      }

      // Update password and clear reset token
      user.password = password;
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      user.refreshTokens = []; // Logout from all devices
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Password has been reset successfully',
      });
    } catch (error) {
      console.error('Reset password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async verifyEmail(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { token } = req.body;

      if (!token) {
        res.status(400).json({
          success: false,
          message: 'Verification token is required',
        });
        return;
      }

      const user = await User.findOne({
        emailVerificationCode: token,
        emailVerificationExpires: { $gt: new Date() },
      });

      if (!user) {
        res.status(400).json({
          success: false,
          message: 'Invalid or expired verification code',
        });
        return;
      }

      // Mark email as verified
      user.isEmailVerified = true;
      user.emailVerificationCode = undefined;
      user.emailVerificationExpires = undefined;
      await user.save();

      // Generate tokens for automatic login
      const tokenPayload = {
        userId: user._id,
        email: user.email,
        role: user.role,
      };

      const { accessToken, refreshToken } = JWTUtils.generateTokenPair(tokenPayload);

      // Save refresh token
      user.refreshTokens.push(refreshToken);
      await user.save();

      res.status(200).json({
        success: true,
        message: 'Email verified successfully',
        data: {
          user: user.toJSON(),
          accessToken,
          refreshToken,
        },
      });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async resendVerificationEmail(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { email } = req.body;

      if (!email) {
        res.status(400).json({
          success: false,
          message: 'Email is required',
        });
        return;
      }

      const user = await User.findOne({ email });

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      if (user.isEmailVerified) {
        res.status(400).json({
          success: false,
          message: 'Email is already verified',
        });
        return;
      }

      // Generate new verification code
      user.emailVerificationCode = CryptoUtils.generateOTP(6);
      user.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      await user.save();

      // Send verification email
      try {
        await EmailService.sendEmailVerification(user, user.emailVerificationCode);
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        res.status(500).json({
          success: false,
          message: 'Failed to send verification email',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Verification email sent successfully',
      });
    } catch (error) {
      console.error('Resend verification email error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
