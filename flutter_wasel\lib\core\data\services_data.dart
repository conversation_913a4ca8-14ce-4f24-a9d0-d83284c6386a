class ServiceItem {
  final String key;
  final String label;
  final String icon;
  final String color;
  final String route;

  const ServiceItem({
    required this.key,
    required this.label,
    required this.icon,
    required this.color,
    required this.route,
  });
}

class ServicesData {
  static const List<ServiceItem> services = [
    ServiceItem(
      key: 'shopOrder',
      label: 'Order from Supplier',
      icon: 'storefront',
      color: '#4A90E2',
      route: '/customer/supplier-categories',
    ),
    ServiceItem(
      key: 'sendPackage',
      label: 'Send a Package',
      icon: 'cube',
      color: '#F5A623',
      route: '/customer/packages',
    ),
    ServiceItem(
      key: 'requestPickup',
      label: 'Request Pickup',
      icon: 'arrow_forward',
      color: '#50E3C2',
      route: '/customer/packages',
    ),
    ServiceItem(
      key: 'customDelivery',
      label: 'AI Chat System',
      icon: 'chat_bubble',
      color: '#BD10E0',
      route: '/customer/home',
    ),
    ServiceItem(
      key: 'trackOrders',
      label: 'Track My Orders',
      icon: 'location_on',
      color: '#B8E986',
      route: '/customer/orders',
    ),
    ServiceItem(
      key: 'trackPackages',
      label: 'Track My Packages',
      icon: 'location_on',
      color: '#68E11f',
      route: '/customer/packages',
    ),
  ];
}

class CategoryItem {
  final String key;
  final String label;
  final String icon;
  final String color;
  final String route;

  const CategoryItem({
    required this.key,
    required this.label,
    required this.icon,
    required this.color,
    required this.route,
  });
}

class CategoriesData {
  static const List<CategoryItem> categories = [
    CategoryItem(
      key: 'restaurants',
      label: 'Restaurants',
      icon: 'restaurant',
      color: '#4A90E2',
      route: '/customer/suppliers-map',
    ),
    CategoryItem(
      key: 'clothings',
      label: 'Clothings',
      icon: 'checkroom',
      color: '#F5A623',
      route: '/customer/suppliers-map',
    ),
    CategoryItem(
      key: 'pharmacies',
      label: 'Pharmacies',
      icon: 'local_pharmacy',
      color: '#50E3C2',
      route: '/customer/suppliers-map',
    ),
    CategoryItem(
      key: 'supermarkets',
      label: 'Supermarkets',
      icon: 'shopping_cart',
      color: '#BD10E0',
      route: '/customer/suppliers-map',
    ),
  ];
}
