import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/auth_models.dart';
import '../../data/models/user_model.dart';
import '../../data/repositories/auth_repository.dart';

// Auth State
class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final UserModel? user;
  final String? error;

  const AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    UserModel? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      error: error,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;

  AuthNotifier(this._authRepository) : super(const AuthState()) {
    _checkAuthStatus();
  }

  // Check initial auth status
  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final isLoggedIn = await _authRepository.isLoggedIn();
      if (isLoggedIn) {
        final user = await _authRepository.getCurrentUser();
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: user,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: false,
          user: null,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isAuthenticated: false,
        error: e.toString(),
      );
    }
  }

  // Login (Demo mode - bypasses backend)
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1000));

      // Create a demo user based on email
      final isCustomer = email.toLowerCase().contains('customer') ||
                        email.toLowerCase().contains('user') ||
                        !email.toLowerCase().contains('supplier');

      final demoUser = UserModel(
        id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        firstName: isCustomer ? 'Demo' : 'Supplier',
        lastName: isCustomer ? 'Customer' : 'Demo',
        role: isCustomer ? 'customer' : 'supplier',
        isEmailVerified: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      state = state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        user: demoUser,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Signup (Demo mode - bypasses backend)
  Future<bool> signup({
    required String email,
    required String password,
    String? role,
    String? supplierId,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1000));

      // Use provided role or default based on email
      final userRole = role ??
                      (email.toLowerCase().contains('supplier') ? 'supplier' : 'customer');

      final demoUser = UserModel(
        id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        firstName: firstName ?? (userRole == 'customer' ? 'Demo' : 'Supplier'),
        lastName: lastName ?? (userRole == 'customer' ? 'Customer' : 'Demo'),
        phoneNumber: phoneNumber,
        role: userRole,
        supplierId: supplierId,
        isEmailVerified: true,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      state = state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        user: demoUser,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Forgot Password
  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final request = ForgotPasswordRequest(email: email);
      final response = await _authRepository.forgotPassword(request);
      
      state = state.copyWith(isLoading: false);
      
      if (!response.success) {
        state = state.copyWith(error: response.message);
      }
      
      return response.success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Reset Password
  Future<bool> resetPassword(String token, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final request = ResetPasswordRequest(token: token, password: password);
      final response = await _authRepository.resetPassword(request);
      
      state = state.copyWith(isLoading: false);
      
      if (!response.success) {
        state = state.copyWith(error: response.message);
      }
      
      return response.success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await _authRepository.logout();
      state = const AuthState(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    } catch (e) {
      // Even if logout fails on server, clear local state
      state = const AuthState(
        isLoading: false,
        isAuthenticated: false,
        user: null,
      );
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Refresh user data
  Future<void> refreshUser() async {
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        state = state.copyWith(user: user);
      }
    } catch (e) {
      // Handle error silently or show notification
    }
  }
}

// Auth Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authRepository = ref.read(authRepositoryProvider);
  return AuthNotifier(authRepository);
});

// Convenience providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
