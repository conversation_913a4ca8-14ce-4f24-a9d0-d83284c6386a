import { Stack/*, useLocalSearchParams*/ } from 'expo-router';

import { Container } from '~/components/Container';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function Signup() {
  //const { user-name } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen options={{ title: 'Signup' }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <ScreenContent path="app/authentication/signup.tsx" title={`Signup`} />
      </Container>
    </>
  );
}
