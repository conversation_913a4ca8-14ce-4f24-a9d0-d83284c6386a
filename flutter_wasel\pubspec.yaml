name: flutter_wasel
description: "Flutter version of Wasel delivery application"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # Navigation
  go_router: ^12.1.3

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI & Styling
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Forms & Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: 11.0.0

  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  geolocator: ^10.1.0

  # Firebase (temporarily disabled for web compatibility)
  # firebase_core: ^2.24.2
  # firebase_messaging: ^14.7.10
  # firebase_auth: ^4.15.3

  # Utilities
  intl: ^0.19.0
  url_launcher: ^6.2.2
  image_picker: ^1.0.4
  permission_handler: ^11.1.0

  # Icons
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/animations/

  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
