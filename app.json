{"expo": {"name": "wasel", "slug": "wasel", "version": "1.0.0", "owner": "fadihamad40984", "scheme": "wasel", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-font", ["expo-location", {"locationWhenInUsePermission": "Show current location on map."}], "expo-image-picker"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "package": "com.omarj2004.wasel", "config": {"googleMaps": {"apiKey": "process.env.GOOGLE_MAPS_API_KEY"}}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "extra": {"router": {}, "eas": {"projectId": "494baf41-130b-4eb1-9a02-4693e56e1a28"}}}}