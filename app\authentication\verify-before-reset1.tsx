import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function VerifyItsYou1() {

  return (
    <>
      <Stack.Screen options={{ title: 'VerifyItsYou1' }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <ScreenContent path="app/authentication/verify-before-reset1.tsx" title={`VerifyItsYou1`} />
      </Container>
    </>
  );
}