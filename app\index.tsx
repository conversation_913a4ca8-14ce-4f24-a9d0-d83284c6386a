import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function Login() {
  return (
    <>
      <Stack.Screen options={{ title: 'Login'}}/>
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={100} paddingBottom={100} style={{ width: '90%' }}>
        <ScreenContent path="app/index.tsx" title="Login"></ScreenContent>
      </Container>
    </>
  );
}



