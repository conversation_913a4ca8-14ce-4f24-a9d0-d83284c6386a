import { useState } from 'react';
import { Dimensions, Pressable } from 'react-native';
import { View, Text, Card, Button, YStack } from 'tamagui';
import { <PERSON><PERSON>View } from 'moti';
import { suppliersData } from '~/temp-data/suppliersData';
import { router } from 'expo-router';
import { Image } from 'react-native';

type Addition = { id: string; name: string; price: number }

type Product = {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  }

type Supplier = {
        id: string;
        name: string;
        lat: number;
        lng: number;
        banner: string;
        logoUrl: string;
        rating: number;
        deliveryTime: string;
        openHours: string;
        tags: string[];
        phone: string;
        category: string;
        products: Product[];
    };

export default function SuppliersMap() {
  const { width, height } = Dimensions.get('window');
  const [selected, setSelected] = useState<Supplier | null>(null);

  return (
    <View flex={1}>
      {/* Mock Map Background */}
      <View
        style={{
          width,
          height,
          backgroundColor: '#e8f4f8',
          position: 'relative',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        {/* Map Grid Pattern */}
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1
        }}>
          {Array.from({ length: 20 }).map((_, i) => (
            <View key={`h-${i}`} style={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: i * (height / 20),
              height: 1,
              backgroundColor: '#666'
            }} />
          ))}
          {Array.from({ length: 15 }).map((_, i) => (
            <View key={`v-${i}`} style={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: i * (width / 15),
              width: 1,
              backgroundColor: '#666'
            }} />
          ))}
        </View>

        {/* Center Location Indicator */}
        <View style={{
          width: 20,
          height: 20,
          borderRadius: 10,
          backgroundColor: '#007AFF',
          borderWidth: 3,
          borderColor: '#fff',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 3,
          elevation: 5,
        }} />

        {/* Supplier Markers */}
        {suppliersData.map((sup, index) => {
          // Simple positioning calculation for demo
          const x = (width / 2) + (index % 3 - 1) * 80 + (Math.random() - 0.5) * 100;
          const y = (height / 2) + (Math.floor(index / 3) - 1) * 80 + (Math.random() - 0.5) * 100;

          return (
            <View
              key={sup.id}
              style={{
                position: 'absolute',
                left: Math.max(25, Math.min(x, width - 75)),
                top: Math.max(25, Math.min(y, height - 75)),
              }}
            >
              <Pressable onPress={() => setSelected(sup)}>
                <View
                  style={{
                    width: 50,
                    height: 50,
                    borderRadius: 25,
                    overflow: 'hidden',
                    borderWidth: 2,
                    borderColor: '#fff',
                    backgroundColor: '#eee',
                    justifyContent: 'center',
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.3,
                    shadowRadius: 3,
                    elevation: 5,
                  }}
                >
                  <Image
                    source={{ uri: sup.logoUrl }}
                    style={{ width: '100%', height: '100%' }}
                    resizeMode="cover"
                  />
                </View>
              </Pressable>
            </View>
          );
        })}
      </View>

      {selected && (
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing' }}
          style={{ position: 'absolute', bottom: 80, left: 20, right: 20 }}
        >
          <Card p="$4" br="$6" elevate bw={1} bc="$gray5" bg="white">
            <YStack gap="$2">
              <Text fontSize="$6" fontWeight="700">{selected.name}</Text>
              <Text fontSize="$4" color="$gray9">{selected.category}</Text>
              <Button onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: selected.id }
              })}>
                View
              </Button>
              <Button variant="outlined" onPress={() => setSelected(null)}>Close</Button>
            </YStack>
          </Card>
        </MotiView>
      )}
    </View>
  );
}
