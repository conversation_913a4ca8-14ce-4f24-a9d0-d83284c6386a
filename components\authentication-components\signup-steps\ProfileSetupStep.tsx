import React from 'react';
import { YStack, Text, H5, XStack, RadioGroup, Label } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { CustomTextField } from '../../CustomTextField';
import { Ionicons } from '@expo/vector-icons';
import { Pressable } from 'react-native';

export const ProfileSetupStep = () => {
  const { signupData, updateSignupData } = useSignupStore();

  const genderOptions = [
    { value: 'male', label: 'Male', icon: 'man' },
    { value: 'female', label: 'Female', icon: 'woman' },
    { value: 'other', label: 'Other', icon: 'person' },
  ];

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="person-circle" size={48} color="#7529B3" />
        <H5 textAlign="center" color="$primary">Profile Setup</H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          Complete your profile with additional details
        </Text>
      </YStack>

      <YStack gap="$4">
        <CustomTextField
          label="Username"
          placeholder="Choose a unique username"
          value={signupData.username}
          onChangeText={(text) => updateSignupData({ username: text.toLowerCase() })}
          icon="at"
          autoCapitalize="none"
          required
        />

        <CustomTextField
          label="Date of Birth"
          placeholder="YYYY-MM-DD"
          value={signupData.dateOfBirth}
          onChangeText={(text) => updateSignupData({ dateOfBirth: text })}
          icon="calendar"
          required
        />

        <YStack gap="$3">
          <Label fontSize="$4" fontWeight="600" color="$gray11">
            Gender *
          </Label>
          <XStack gap="$3" justifyContent="space-around">
            {genderOptions.map((option) => (
              <Pressable
                key={option.value}
                onPress={() => updateSignupData({ gender: option.value as any })}
                style={{
                  flex: 1,
                  padding: 16,
                  borderRadius: 12,
                  borderWidth: 2,
                  borderColor: signupData.gender === option.value ? '#7529B3' : '#e5e5e5',
                  backgroundColor: signupData.gender === option.value ? '#f3f0ff' : '#fff',
                  alignItems: 'center',
                  gap: 8,
                }}
              >
                <Ionicons 
                  name={option.icon as any} 
                  size={24} 
                  color={signupData.gender === option.value ? '#7529B3' : '#666'} 
                />
                <Text 
                  fontSize="$3" 
                  fontWeight="500"
                  color={signupData.gender === option.value ? '$primary' : '$gray10'}
                >
                  {option.label}
                </Text>
              </Pressable>
            ))}
          </XStack>
        </YStack>
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          This information helps us personalize your experience
        </Text>
      </YStack>
    </YStack>
  );
};
