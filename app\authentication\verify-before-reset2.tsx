import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function VerifyItsYou2() {

  return (
    <>
      <Stack.Screen options={{ title: 'VerifyItsYou2' }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <ScreenContent path="app/authentication/verify-before-reset2.tsx" title={`VerifyItsYou2`} />
      </Container>
    </>
  );
}