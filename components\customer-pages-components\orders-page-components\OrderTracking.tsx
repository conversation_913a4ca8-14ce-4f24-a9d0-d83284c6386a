import { Platform, ScrollView, TouchableWithoutFeedback } from 'react-native';
import { View, Text, Button, XStack, YStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useMyOrdersStore } from './useMyOrdersStore';
import { useEffect, useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { Dimensions } from 'react-native';
import { Linking } from 'react-native';

export function OrderTracking() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === id));

  // If order not found, redirect
  if (!order || !order.address) {
    router.replace('/orders');
    return null;
  } 

  // Mock customer location (you could geocode order.address here)
  const customerLocation = {
    latitude: order.address.lat,
    longitude: order.address.lng,
  };

  // Driver starting point near customer (mock)
  const [driverLocation, setDriverLocation] = useState({
    latitude: customerLocation.latitude - 0.01,
    longitude: customerLocation.longitude - 0.01,
  });

  // Simulate driver moving towards customer location
  useEffect(() => {
    const interval = setInterval(() => {
      setDriverLocation((prev) => {
        const latDiff = customerLocation.latitude - prev.latitude;
        const lonDiff = customerLocation.longitude - prev.longitude;
        const step = 0.0003; // movement per tick

        // Calculate next position
        let newLat = prev.latitude + Math.sign(latDiff) * Math.min(Math.abs(latDiff), step);
        let newLon = prev.longitude + Math.sign(lonDiff) * Math.min(Math.abs(lonDiff), step);

        // If driver close enough to customer, stop moving
        if (Math.abs(latDiff) < 0.0001 && Math.abs(lonDiff) < 0.0001) {
          clearInterval(interval);
          return prev;
        }
        return { latitude: newLat, longitude: newLon };
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // ETA countdown (mock)
  const [eta, setEta] = useState(30 * 60); // 30 minutes in seconds

  useEffect(() => {
    if (eta <= 0) return;
    const timer = setInterval(() => {
      setEta((e) => (e > 0 ? e - 1 : 0));
    }, 1000);
    return () => clearInterval(timer);
  }, [eta]);

  const formatTime = (secs: number) => {
    const m = Math.floor(secs / 60);
    const s = secs % 60;
    return `${m}m ${s}s`;
  };

  // Status steps for progress UI
  const statuses = ['Preparing', 'On the Way', 'Delivered'];

  const { height } = Dimensions.get('window');

  const [fullscreen, setFullscreen] = useState(false);

  return (
    <>
    {fullscreen && (
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 999,
        }}
      >
        <MapboxGL.MapView style={{ flex: 1 }}>
          <MapboxGL.Camera
            centerCoordinate={[customerLocation.longitude, customerLocation.latitude]}
            zoomLevel={13}
          />
          <MapboxGL.MarkerView coordinate={[customerLocation.longitude, customerLocation.latitude]}>
            <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'green' }} />
          </MapboxGL.MarkerView>
          <MapboxGL.MarkerView coordinate={[driverLocation.longitude, driverLocation.latitude]}>
            <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'purple' }} />
          </MapboxGL.MarkerView>
        </MapboxGL.MapView>

        <Button
          onPress={() => setFullscreen(false)}
          bg='$primary'
          color='white'
          style={{
            position: 'absolute',
            top: 40,
            right: 20,
            zIndex: 1000,
            padding: 10
          }}
          br={100}
          hoverStyle={{ bg: "$third" }} 
          pressStyle={{ bg: "$third" }}
        >
          <Ionicons name='close' size={20} color='#fff'></Ionicons>
        </Button>
      </View>
    )}
    <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}>
      

        {/* Map */}
        {!fullscreen && (
          <>
          {/* Header */}
          <View
            style={{
              paddingVertical: 10,
              paddingHorizontal: 6,
              borderBottomLeftRadius: 32,
              borderBottomRightRadius: 32,
              backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
              backgroundColor: '#7529B3',
            }}
          >
            <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
              <Text fontSize="$8" fontWeight="700" color="white" textAlign="center">
                Tracking Order #{order.id}
              </Text>
            </MotiView>
          </View>

          <YStack p="$4" gap="$4">

            {/* Status Progress Bar */}
            <XStack justifyContent="space-between" mb="$4" px="$2">
              {statuses.map((status, i) => {
                const active = statuses.indexOf(order.status) >= i;
                return (
                  <YStack key={status} ai="center">
                    <Ionicons
                      name={
                        status === 'Delivered'
                          ? 'checkmark-circle'
                          : status === 'On the Way'
                          ? 'bicycle'
                          : 'construct-outline'
                      }
                      size={32}
                      color={active ? '#7529B3' : '#ccc'}
                    />
                    <Text mt="$1" color={active ? '#7529B3' : '#ccc'}>
                      {status}
                    </Text>
                  </YStack>
                );
              })}
            </XStack>

            {/* ETA Timer */}
            <Text fontSize="$6" mb="$4">
              Estimated Arrival: {formatTime(eta)}
            </Text>

            {/* Driver Info */}
            {order.driverName && (
              <XStack justifyContent='space-between'>
                <YStack
                  br="$6"
                  p="$3"
                  bg="#f3f3f3"
                  mb="$4"
                  borderWidth={1}
                  borderColor="#ddd"
                  width="85%"
                >
                  <Text fontWeight="600" fontSize="$5">
                    Driver: {order.driverName}
                  </Text>
                </YStack>
                <Button
                  circular
                  icon={<Ionicons name="call" size={20} color="white" />}
                  onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                  mt="$2"
                  bg="$blue10"
                  br="$8"
                >
                </Button>
              </XStack>
            )}

            <View
              bg='$fourth'
              style={{
                padding: 10,
                borderRadius: 10,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text fontSize="$5" color="$primary" fontWeight="600">
                Tap the map to view and move freely
              </Text>
            </View>

            <TouchableWithoutFeedback onPress={() => setFullscreen(true)}>
              <View style={{ height: height * 0.375, borderRadius: 12, overflow: 'hidden' }}>
                <MapboxGL.MapView style={{ flex: 1, borderRadius: 12 }}>
                  <MapboxGL.Camera
                    centerCoordinate={[customerLocation.longitude, customerLocation.latitude]}
                    zoomLevel={13}
                  />
                  <MapboxGL.MarkerView coordinate={[customerLocation.longitude, customerLocation.latitude]}>
                    <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'green' }} />
                  </MapboxGL.MarkerView>
                  <MapboxGL.MarkerView coordinate={[driverLocation.longitude, driverLocation.latitude]}>
                    <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'purple' }} />
                  </MapboxGL.MarkerView>
                </MapboxGL.MapView>
              </View>
            </TouchableWithoutFeedback>

            {/* Back Button */}
            <Button
              mt="$4"
              size="$5"
              bg="$primary"
              color="white"
              br="$10"
              onPress={() => router.back()}
              hoverStyle={{ bg: "$third" }} 
              pressStyle={{ bg: "$third" }}
            >
              Back to Orders
            </Button>

          </YStack>
          </>
        )}

    </ScrollView>
    </>
  );
}
