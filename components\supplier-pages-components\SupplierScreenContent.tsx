import React from 'react';
import SupplierHomeGUI from './home-page-components/SupplierHomeGUI';
import SupplierOrderDetails from './home-page-components/SupplierOrderDetails';
import SupplierTracking from './home-page-components/SupplierTracking';
import SupplierProfileGUI from './profile-page-components/SupplierProfileGUI';

type SupplierScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const SupplierScreenContent = ({ title, path, children }: SupplierScreenContentProps) => {

  return ((title==="SupplierHomeGUI") ? (
      <SupplierHomeGUI />
    ) : (title==="SupplierOrderDetails") ? (
      <SupplierOrderDetails />
    ) : (title==="SupplierTracking") ? (
      <SupplierTracking />
    ) : (title==="SupplierProfileGUI") ? (
      <SupplierProfileGUI />
    ) : (
      null
    )
  );
};