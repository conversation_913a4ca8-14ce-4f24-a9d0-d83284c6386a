import { create } from 'zustand';

export interface SignupData {
  // Step 1: Basic Info
  firstName: string;
  lastName: string;
  email: string;
  
  // Step 2: Contact & Security
  phone: string;
  password: string;
  confirmPassword: string;
  
  // Step 3: Profile Setup
  username: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other' | '';
  
  // Step 4: Address Info
  address: string;
  city: string;
  country: string;
  
  // Step 5: Business Info (for suppliers only)
  userType: 'customer' | 'supplier';
  storeName?: string;
  businessType?: string;
  openHours?: string;
  
  // Step 6: Location & Preferences
  location?: [number, number]; // [lng, lat]
  notifications: boolean;
  terms: boolean;
}

interface SignupStore {
  currentStep: number;
  totalSteps: number;
  signupData: SignupData;
  
  // Actions
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  updateSignupData: (data: Partial<SignupData>) => void;
  resetSignup: () => void;
  isStepValid: (step: number) => boolean;
}

const initialSignupData: SignupData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  username: '',
  dateOfBirth: '',
  gender: '',
  address: '',
  city: '',
  country: '',
  userType: 'customer',
  storeName: '',
  businessType: '',
  openHours: '',
  location: undefined,
  notifications: true,
  terms: false,
};

export const useSignupStore = create<SignupStore>((set, get) => ({
  currentStep: 1,
  totalSteps: 6,
  signupData: initialSignupData,
  
  setCurrentStep: (step) => set({ currentStep: step }),
  
  nextStep: () => set((state) => ({ 
    currentStep: Math.min(state.currentStep + 1, state.totalSteps) 
  })),
  
  prevStep: () => set((state) => ({ 
    currentStep: Math.max(state.currentStep - 1, 1) 
  })),
  
  updateSignupData: (data) => set((state) => ({
    signupData: { ...state.signupData, ...data }
  })),
  
  resetSignup: () => set({
    currentStep: 1,
    signupData: initialSignupData,
  }),
  
  isStepValid: (step) => {
    const { signupData } = get();
    
    switch (step) {
      case 1: // Basic Info
        return !!(signupData.firstName && signupData.lastName && signupData.email);
      case 2: // Contact & Security
        return !!(signupData.phone && signupData.password && signupData.confirmPassword && 
                 signupData.password === signupData.confirmPassword);
      case 3: // Profile Setup
        return !!(signupData.username && signupData.dateOfBirth && signupData.gender);
      case 4: // Address Info
        return !!(signupData.address && signupData.city && signupData.country);
      case 5: // Business Info (conditional)
        if (signupData.userType === 'supplier') {
          return !!(signupData.storeName && signupData.businessType && signupData.openHours);
        }
        return true; // Skip for customers
      case 6: // Final step
        return signupData.terms;
      default:
        return false;
    }
  },
}));
